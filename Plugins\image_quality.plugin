"""
Image Quality Control Plugin for exteraGram
Allows users to control the quality of sent images with a scale from 1 to 100.

This plugin hooks into Telegram's image compression pipeline and replaces
the hardcoded quality values with user-configurable settings.
"""

from base_plugin import BasePlugin, MethodHook
from ui.settings import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Switch, Text
from android_utils import log, find_class
from android.graphics import Bitmap
from java.lang import Boolean
import traceback

__id__ = "image_quality"
__version__ = "1.0.0"
__author__ = "exteraGram"
__description__ = "Control image quality when sending photos (1-100 scale)"

class ImageQualityPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.hook_refs = []
        self.enabled = True
    
    def on_plugin_load(self):
        """Called when plugin is loaded"""
        try:
            self._setup_hooks()
            self.log("✅ Image Quality Plugin loaded successfully")
        except Exception as e:
            self.log(f"❌ Failed to load plugin: {e}\n{traceback.format_exc()}")
    
    def on_plugin_unload(self):
        """Called when plugin is unloaded"""
        try:
            self._remove_hooks()
            self.log("🔄 Image Quality Plugin unloaded")
        except Exception as e:
            self.log(f"❌ Error during unload: {e}")
    
    def _setup_hooks(self):
        """Setup method hooks for image quality control"""
        try:
            # Get ImageLoader class
            image_loader_class = find_class("org.telegram.messenger.ImageLoader")
            
            # Hook method 1: scaleAndSaveImage(Bitmap, float, float, int, boolean)
            # Used for thumbnails with quality=55
            method1 = image_loader_class.getClass().getDeclaredMethod(
                "scaleAndSaveImage", 
                Bitmap.getClass(), 
                float.class, 
                float.class, 
                int.class, 
                Boolean.TYPE
            )
            hook1 = QualityHook(self, "thumbnail")
            hook_ref1 = self.hook_method(method1, hook1)
            if hook_ref1:
                self.hook_refs.append(hook_ref1)
                self.log("🎯 Hooked thumbnail quality method")
            
            # Hook method 2: scaleAndSaveImage(Bitmap, float, float, boolean, int, boolean, int, int)
            # Used for main photos with quality=80
            method2 = image_loader_class.getClass().getDeclaredMethod(
                "scaleAndSaveImage",
                Bitmap.getClass(),
                float.class,
                float.class, 
                Boolean.TYPE,
                int.class,
                Boolean.TYPE,
                int.class,
                int.class
            )
            hook2 = QualityHook(self, "main")
            hook_ref2 = self.hook_method(method2, hook2)
            if hook_ref2:
                self.hook_refs.append(hook_ref2)
                self.log("🎯 Hooked main photo quality method")
            
            if len(self.hook_refs) == 2:
                self.log("✅ Successfully hooked all ImageLoader.scaleAndSaveImage methods")
            else:
                self.log(f"⚠️ Only {len(self.hook_refs)}/2 hooks applied successfully")
            
        except Exception as e:
            self.log(f"❌ Failed to setup hooks: {e}\n{traceback.format_exc()}")
    
    def _remove_hooks(self):
        """Remove all method hooks"""
        for hook_ref in self.hook_refs:
            if hook_ref:
                try:
                    self.unhook_method(hook_ref)
                except Exception as e:
                    self.log(f"⚠️ Error removing hook: {e}")
        self.hook_refs.clear()
    
    def create_settings(self):
        """Create plugin settings UI"""
        return [
            Header("🖼️ Image Quality Control"),
            
            Switch(
                key="enabled",
                text="Enable Quality Control",
                icon="msg_photo",
                default=True,
                subtext="Enable/disable image quality modification",
                on_change=self._on_enabled_change
            ),
            
            Slider(
                key="image_quality",
                text="Image Quality",
                icon="msg_photo",
                default=80,
                min_value=1,
                max_value=100,
                subtext="Quality level for sent images (1-100). Higher = better quality but larger files."
            ),
            
            Text(
                text="📊 Quality Guide:",
                subtext="• 1-30: Very low quality, small files\n• 31-60: Low quality, medium files\n• 61-80: Good quality (default)\n• 81-95: High quality, large files\n• 96-100: Maximum quality, very large files"
            ),
            
            Switch(
                key="apply_to_thumbnails",
                text="Apply to Thumbnails",
                icon="msg_photo",
                default=True,
                subtext="Apply quality setting to image thumbnails as well"
            ),
            
            Switch(
                key="debug_logs",
                text="Debug Logging",
                icon="msg_log",
                default=False,
                subtext="Enable detailed logging for debugging"
            )
        ]
    
    def _on_enabled_change(self, enabled):
        """Handle enable/disable toggle"""
        self.enabled = enabled
        if enabled:
            self.log("🟢 Image quality control enabled")
        else:
            self.log("🔴 Image quality control disabled")

class QualityHook(MethodHook):
    """Hook class for intercepting and modifying image quality parameters"""
    
    def __init__(self, plugin, method_type):
        super().__init__()
        self.plugin = plugin
        self.method_type = method_type  # "thumbnail" or "main"
    
    def before_hooked_method(self, param):
        """Called before the hooked method executes"""
        try:
            # Check if plugin is enabled
            if not self.plugin.get_setting("enabled", True):
                return
            
            # Get user-configured quality
            quality = self.plugin.get_setting("image_quality", 80)
            
            # Ensure quality is within valid range
            quality = max(1, min(100, quality))
            
            # Apply quality based on method type
            if self.method_type == "thumbnail":
                # Method: scaleAndSaveImage(Bitmap, float, float, int, boolean)
                # Quality parameter is at index 3
                if self.plugin.get_setting("apply_to_thumbnails", True):
                    original_quality = param.args[3]
                    param.args[3] = quality
                    
                    if self.plugin.get_setting("debug_logs", False):
                        self.plugin.log(f"🔧 Thumbnail quality: {original_quality} → {quality}")
                        
            elif self.method_type == "main":
                # Method: scaleAndSaveImage(Bitmap, float, float, boolean, int, boolean, int, int)
                # Quality parameter is at index 4
                original_quality = param.args[4]
                param.args[4] = quality
                
                if self.plugin.get_setting("debug_logs", False):
                    self.plugin.log(f"🔧 Main photo quality: {original_quality} → {quality}")
                
        except Exception as e:
            self.plugin.log(f"❌ Error in QualityHook ({self.method_type}): {e}")
