# Image Quality Control Plugin - Developer Documentation

## Overview
This plugin provides user-configurable image quality control for exteraGram by hooking into Telegram's image compression pipeline.

## Technical Implementation

### Core Concept
The plugin intercepts calls to `ImageLoader.scaleAndSaveImage()` methods and replaces hardcoded quality parameters with user-configured values.

### Hooked Methods
1. **Thumbnail Method**: `scaleAndSaveImage(Bitmap, float, float, int, boolean)`
   - Original quality: 55
   - Quality parameter at index 3
   - Used in `SendMessagesHelper.generatePhotoSizes()` line 7769

2. **Main Photo Method**: `scaleAndSaveImage(Bitmap, float, float, boolean, int, boolean, int, int)`
   - Original quality: 80  
   - Quality parameter at index 4
   - Used in `SendMessagesHelper.generatePhotoSizes()` line 7773

### Hook Implementation
```python
class QualityHook(MethodHook):
    def before_hooked_method(self, param):
        quality = self.plugin.get_setting("image_quality", 80)
        
        if self.method_type == "thumbnail":
            param.args[3] = quality  # 4th parameter
        elif self.method_type == "main":
            param.args[4] = quality  # 5th parameter
```

### Method Signatures
```java
// Thumbnail method
public static TLRPC.PhotoSize scaleAndSaveImage(
    Bitmap bitmap, 
    float maxWidth, 
    float maxHeight, 
    int quality,      // <- Modified parameter
    boolean cache
)

// Main photo method  
public static TLRPC.PhotoSize scaleAndSaveImage(
    Bitmap bitmap,
    float maxWidth,
    float maxHeight, 
    boolean progressive,
    int quality,      // <- Modified parameter
    boolean cache,
    int minWidth,
    int minHeight
)
```

## Code Flow Analysis

### Original Telegram Flow
1. `SendMessagesHelper.prepareSendingPhoto()` called
2. `prepareSendingMedia()` executed in background thread
3. `generatePhotoSizes()` creates thumbnail and main photo
4. `ImageLoader.scaleAndSaveImage()` called with hardcoded quality values
5. `scaleAndSaveImageInternal()` performs actual compression
6. `bitmap.compress(compressFormat, quality, stream)` - Android's compression

### Plugin Intervention
1. Plugin hooks `scaleAndSaveImage()` methods during `on_plugin_load()`
2. When image is sent, `before_hooked_method()` is triggered
3. Quality parameter is replaced with user setting
4. Original method continues with modified quality
5. Result: User-controlled compression quality

## Settings Architecture
```python
def create_settings(self):
    return [
        Header("🖼️ Image Quality Control"),
        Switch(key="enabled", default=True),
        Slider(key="image_quality", min_value=1, max_value=100, default=80),
        Switch(key="apply_to_thumbnails", default=True),
        Switch(key="debug_logs", default=False)
    ]
```

## Error Handling
- Try-catch blocks around all hook operations
- Graceful degradation if hooks fail to apply
- Detailed logging for debugging
- Quality value validation (1-100 range)

## Performance Considerations
- Minimal overhead: only parameter modification
- No additional image processing
- Hooks applied once during plugin load
- No impact on non-image messages

## Testing Scenarios
1. **Quality Range**: Test values 1, 25, 50, 75, 100
2. **Toggle States**: Enable/disable plugin functionality
3. **Thumbnail Control**: With/without thumbnail quality modification
4. **Error Conditions**: Invalid quality values, hook failures
5. **Performance**: Large images, multiple images, rapid sending

## Future Enhancements
- Per-chat quality settings
- Automatic quality based on file size limits
- Preview of estimated file size
- Quality presets (Low/Medium/High/Ultra)
- Integration with data saver modes

## Dependencies
- `base_plugin.BasePlugin` - Core plugin functionality
- `base_plugin.MethodHook` - Xposed method hooking
- `ui.settings.*` - Settings UI components
- `android_utils.find_class` - Java class resolution
- `android.graphics.Bitmap` - Android bitmap class

## Compatibility Notes
- Requires exteraGram with Xposed support
- Android 7.0+ (API level 24+)
- Works with all image formats supported by Telegram
- Independent of Telegram version updates (hooks internal methods)

## Debugging
Enable debug logs to see:
- Hook application success/failure
- Original vs modified quality values
- Method call interception
- Error details with stack traces

## Known Limitations
- Only affects outgoing images
- Cannot modify already sent images
- Quality 100 may cause very large files
- Some Android devices may have compression limits
