# Image Quality Control Plugin

## Описание
Плагин для контроля качества отправляемых изображений в Telegram с возможностью настройки уровня качества от 1 до 100.

## Возможности
- 🎛️ Настройка качества изображений по шкале от 1 до 100
- 🖼️ Контроль качества как основных фото, так и миниатюр
- 📊 Подробное руководство по уровням качества
- 🔧 Отладочные логи для диагностики
- ⚡ Возможность включения/отключения без перезагрузки

## Как работает
Плагин перехватывает вызовы методов `ImageLoader.scaleAndSaveImage()` в Telegram и заменяет стандартные значения качества (55 для миниатюр, 80 для основных фото) на пользовательские настройки.

## Настройки

### Основные настройки
- **Enable Quality Control**: Включить/отключить контроль качества
- **Image Quality**: Уровень качества изображений (1-100)
- **Apply to Thumbnails**: Применять настройки качества к миниатюрам
- **Debug Logging**: Включить подробные логи для отладки

### Руководство по качеству
- **1-30**: Очень низкое качество, маленькие файлы
- **31-60**: Низкое качество, средние файлы  
- **61-80**: Хорошее качество (по умолчанию в Telegram)
- **81-95**: Высокое качество, большие файлы
- **96-100**: Максимальное качество, очень большие файлы

## Технические детали

### Перехватываемые методы
1. `scaleAndSaveImage(Bitmap, float, float, int, boolean)` - для миниатюр
2. `scaleAndSaveImage(Bitmap, float, float, boolean, int, boolean, int, int)` - для основных фото

### Параметры качества
- **Стандартное качество миниатюр**: 55
- **Стандартное качество основных фото**: 80
- **Пользовательское качество**: 1-100 (настраивается)

## Установка
1. Скопируйте файл `image_quality.plugin` в папку `Plugins/`
2. Перезапустите exteraGram
3. Перейдите в настройки плагинов
4. Найдите "Image Quality Control" и настройте параметры

## Использование
1. Включите плагин в настройках
2. Установите желаемый уровень качества (1-100)
3. При отправке изображений будет применяться выбранное качество
4. Для отладки можно включить "Debug Logging"

## Примеры использования

### Экономия трафика
- Установите качество 30-50 для экономии мобильного трафика
- Отключите применение к миниатюрам для дополнительной экономии

### Максимальное качество
- Установите качество 95-100 для профессиональных фото
- Включите применение к миниатюрам для лучшего предпросмотра

### Сбалансированный режим
- Используйте качество 70-85 для баланса между качеством и размером файла
- Оставьте настройки миниатюр включенными

## Совместимость
- ✅ exteraGram (все версии)
- ✅ Android 7.0+
- ✅ Работает с любыми форматами изображений

## Известные ограничения
- Плагин влияет только на новые отправляемые изображения
- Не влияет на уже отправленные или полученные изображения
- Качество 100 может значительно увеличить размер файлов

## Устранение неполадок

### Плагин не работает
1. Проверьте, что плагин включен в настройках
2. Убедитесь, что exteraGram перезапущен после установки
3. Включите "Debug Logging" для диагностики

### Качество не изменяется
1. Проверьте настройку "Enable Quality Control"
2. Убедитесь, что значение качества отличается от стандартного (80)
3. Проверьте логи на наличие ошибок

### Большие размеры файлов
1. Снизьте уровень качества
2. Отключите применение к миниатюрам
3. Используйте качество не выше 85 для повседневного использования

## Версия
1.0.0 - Первый релиз

## Автор
exteraGram Team

## Лицензия
Свободное использование в рамках exteraGram
